/* Math Editor Dialog Styles */
.matheditor-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: Arial, sans-serif;
}

.matheditor-content {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    width: 70%;
    max-width: 800px;
    max-height: 90vh;
    overflow: auto;
    padding: 0 0 10px 0; /* Removed top padding to accommodate the header */
    position: relative;
}

.matheditor-container {
    padding: 0 20px;
    margin-top: 10px;
}

.matheditor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    border-bottom: none;
    padding: 0 20px;
    background-color: #1a73e8; /* Primary blue color */
    color: white;
    height: 40px;
    border-radius: 4px 4px 0 0;
}

.matheditor-header h2 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: white;
}

.matheditor-close-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: white;
    padding: 2px;
    line-height: 1;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.15s ease;
}

.matheditor-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.matheditor-input-container {
    margin-bottom: 20px;
    margin-top: 6px;
    padding: 0;
}

.matheditor-label {
    display: block;
    margin-bottom: 4px;
    font-weight: bold;
    color: #555;
    font-size: 12px;
}

#matheditor-input {
    width: 99%;
    font-size: 16px;
    border: 2px solid #2196f3;
    border-radius: 4px;
    min-height: 180px;
    box-shadow: 0 2px 6px rgba(33, 150, 243, 0.15);
    background-color: #f9f9f9;
    transition: all 0.3s;
    margin-bottom: 10px;
}

/* Toolbar Tabs */
.matheditor-toolbar-tabs {
    display: flex;
    flex-wrap: nowrap;
    margin-bottom: 0;
    border-bottom: none;
    padding: 0;
    background: linear-gradient(to right, #f8f9fa, #eef1f5);
    border-radius: 8px 8px 0 0;
    overflow-x: auto;
    overflow-y: hidden;
    height: 36px; /* Reduced height */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1;
}

.matheditor-toolbar-tabs::-webkit-scrollbar {
    height: 3px;
}

.matheditor-toolbar-tabs::-webkit-scrollbar-thumb {
    background-color: rgba(0, 105, 180, 0.3);
    border-radius: 10px;
}

.matheditor-tab-btn {
    padding: 0 16px;
    border: none;
    background-color: transparent;
    cursor: pointer;
    font-size: 12px; /* Slightly smaller font */
    transition: all 0.3s ease;
    margin: 0 2px;
    color: #666;
    position: relative;
    height: 36px; /* Reduced height */
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
}

.matheditor-tab-btn:hover {
    color: #0069b4;
    background-color: rgba(0, 105, 180, 0.05);
}

.matheditor-tab-btn:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background-color: #0069b4;
    transition: all 0.3s ease;
    transform: translateX(-50%);
    opacity: 0;
}

.matheditor-tab-btn:hover:after {
    width: 30%;
    opacity: 0.5;
}

.matheditor-tab-btn.active {
    background-color: #fff;
    color: #0069b4;
    font-weight: 600;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
    z-index: 2;
}

.matheditor-tab-btn.active:after {
    width: 70%;
    opacity: 1;
    height: 3px;
}

/* Toolbar Content */
.matheditor-toolbar {
    display: block;
    margin-bottom: 16px;
    padding: 12px 16px;
    background-color: #ffffff;
    border: none;
    border-radius: 0 0 8px 8px;
    height: auto;
    min-height: 80px;
    max-height: 120px;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.matheditor-tab-content {
    display: none;
    flex-direction: column;
    gap: 8px;
    height: 100%;
    overflow: hidden;
}

.matheditor-tab-content.active {
    display: flex;
}

/* Button Groups Container */
.matheditor-button-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0;
    padding: 0;
}

/* Button Row Container - Main horizontal container */
.matheditor-button-row {
    display: flex;
    align-items: flex-start;
    gap: 0;
    margin-bottom: 0;
    flex-wrap: nowrap;
    overflow-x: auto;
    width: 100%;
    padding: 4px 0;
}

/* Group Divider */
.matheditor-group-divider {
    width: 1px;
    height: 32px;
    background-color: #dadce0;
    margin: 0 12px;
    flex-shrink: 0;
    align-self: flex-start;
}

/* Group Label */
.matheditor-group-label {
    font-size: 9px;
    color: #5f6368;
    text-align: center;
    margin-top: 4px;
    font-weight: 400;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    white-space: nowrap;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Google Docs Style Buttons */
.matheditor-toolbar-btn {
    padding: 4px 6px;
    border: none;
    border-radius: 3px;
    background-color: transparent;
    cursor: pointer;
    font-size: 13px;
    transition: all 0.15s ease;
    min-width: 28px;
    height: 28px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    color: #444;
    box-shadow: none;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

/* Style for FontAwesome icons in buttons */
.matheditor-toolbar-btn i {
    font-size: 14px;
    color: #555;
    transition: all 0.2s ease;
}

/* Style for text next to icons */
.matheditor-toolbar-btn .btn-text {
    margin-left: 4px;
    font-size: 13px;
}

/* Google Docs Style Button Hover Effects */
.matheditor-toolbar-btn:hover {
    background-color: #f1f3f4;
    color: #202124;
    box-shadow: none;
    transform: none;
}

.matheditor-toolbar-btn:hover i {
    color: #202124;
}

.matheditor-toolbar-btn:active {
    background-color: #e8eaed;
    transform: none;
    box-shadow: none;
}

/* Focus state for accessibility */
.matheditor-toolbar-btn:focus {
    outline: 2px solid #4285f4;
    outline-offset: 1px;
    background-color: #f1f3f4;
}

/* Remove old pulse animation */
.matheditor-toolbar-btn:hover::before {
    display: none;
}

.matheditor-toolbar-btn.large {
    padding: 6px 8px;
    font-size: 14px;
    min-width: 32px;
    height: 32px;
    font-weight: 400;
}

.matheditor-toolbar-btn.large i {
    font-size: 15px;
}

/* No longer using category headers */

.matheditor-buttons-container {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 16px;
    padding: 16px 0 0;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.matheditor-cancel-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    background-color: #f2f2f2;
    cursor: pointer;
    font-size: 14px;
    color: #555;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.matheditor-cancel-btn:hover {
    background-color: #e6e6e6;
    color: #333;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.matheditor-cancel-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.matheditor-insert-btn {
    padding: 8px 20px;
    border: none;
    border-radius: 6px;
    background: linear-gradient(135deg, #0069b4, #0078c8);
    color: #fff;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 105, 180, 0.3);
    position: relative;
    overflow: hidden;
}

.matheditor-insert-btn:hover {
    background: linear-gradient(135deg, #005a9e, #0069b4);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 105, 180, 0.4);
}

.matheditor-insert-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 105, 180, 0.3);
}

/* Shine effect on insert button */
.matheditor-insert-btn::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
        to right,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.3) 50%,
        rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    transition: transform 0.5s;
    opacity: 0;
}

.matheditor-insert-btn:hover::after {
    opacity: 1;
    transform: rotate(30deg) translate(50%, -50%);
    transition: transform 0.7s, opacity 0.5s;
}

/* Hide MathLive virtual keyboard toggle button */
.ML__virtual-keyboard-toggle,
[part="virtual-keyboard-toggle"],
[data-ml__tooltip="Toggle Virtual Keyboard"],
[data-command="&quot;toggleVirtualKeyboard&quot;"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    width: 0 !important;
    height: 0 !important;
    position: absolute !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
}

math-field::part(virtual-keyboard-toggle) {
    display: none !important;
}
