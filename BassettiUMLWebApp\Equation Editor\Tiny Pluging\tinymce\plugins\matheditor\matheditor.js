/**
 * MathEditor plugin functionality
 */
class MathEditor {
    constructor(editor, url) {
        this.editor = editor;
        this.url = url;
        this.dialog = null;
        this.mathField = null;
        this.initialLatex = "";
    }

    /**
     * Initialize the math editor
     */
    init() {
        // Register the button
        this.editor.ui.registry.addButton("matheditor", {
            icon: "character-count",
            tooltip: "Insert Math Equation",
            onAction: () => {
                this.openDialog();
            },
        });

        // Add double-click handler for math equations
        this.editor.on("dblclick", (e) => {
            const target = e.target;

            // Check if clicked element is a math equation
            if (
                target.nodeName === "IMG" &&
                target.parentNode &&
                target.parentNode.className === "math-tex"
            ) {
                // Get the LaTeX from the data attribute
                const latex = target.parentNode.getAttribute("data-latex");
                if (latex) {
                    // Select the node to ensure proper replacement later
                    this.editor.selection.select(target.parentNode);

                    // Open the math editor with the existing LaTeX
                    setTimeout(() => {
                        this.openDialog(latex);
                    }, 10);

                    // Prevent default behavior
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Register the command
        this.editor.addCommand("mceMatheditor", (_ui, value) => {
            this.openDialog(value);
        });
    }

    /**
     * Load CSS
     */
    loadCSS() {
        try {
            const cssUrl = this.url + "/matheditor.css";
            console.log("Loading CSS from:", cssUrl);

            // Check if already loaded
            if (document.querySelector(`link[href="${cssUrl}"]`)) {
                console.log("CSS already loaded");
                return; // Already loaded
            }

            const link = document.createElement("link");
            link.rel = "stylesheet";
            link.type = "text/css";
            link.href = cssUrl;

            // Add fallback inline styles if CSS fails to load
            link.onerror = () => {
                console.error(
                    "Failed to load CSS file, applying inline styles"
                );
                this.addInlineStyles();
            };

            document.head.appendChild(link);
            console.log("CSS link added to document head");
        } catch (error) {
            console.error("Error loading CSS:", error);
            // Add fallback inline styles
            this.addInlineStyles();
        }
    }

    /**
     * Add inline styles as fallback
     */
    addInlineStyles() {
        console.log("Adding inline styles as fallback");
        // Instead of adding inline styles, try to load the CSS file again with a different approach
        const link = document.createElement("link");
        link.rel = "stylesheet";
        link.type = "text/css";
        link.href = this.url + "/matheditor.css";
        document.head.appendChild(link);

        console.log(
            "Attempted to load CSS file again with a different approach"
        );
    }

    /**
     * Ensure MathLive is loaded
     */
    ensureMathLive(callback) {
        // Check if MathLive is already available
        if (window.MathLive) {
            console.log("Using existing MathLive instance");
            callback();
            return;
        }

        // If not available, wait a bit and check again
        console.log("Waiting for MathLive to load...");
        const checkInterval = setInterval(() => {
            if (window.MathLive) {
                clearInterval(checkInterval);
                console.log("MathLive is now available");
                callback();
            }
        }, 100);

        // Set a timeout to stop checking after 5 seconds
        setTimeout(() => {
            clearInterval(checkInterval);
            if (!window.MathLive) {
                console.error("MathLive failed to load after waiting");
                alert(
                    "Failed to load the math editor. Please try again later."
                );
            }
        }, 5000);
    }

    /**
     * Create a high-quality SVG URL for the equation
     */
    createLatexUrl(latex) {
        // Using codecogs with SVG format for better quality
        return (
            "https://latex.codecogs.com/svg.latex?" + encodeURIComponent(latex)
        );
    }

    /**
     * Open the math editor dialog
     */
    async openDialog(initialLatex = "") {
        this.initialLatex = initialLatex;

        try {
            // Load CSS
            this.loadCSS();

            // Create dialog by loading the HTML template
            // Note: The dialog is added to the document inside createDialogDirectly
            // and MathLive is initialized there as well
            this.createDialogDirectly();
        } catch (error) {
            console.error("Error opening math editor dialog:", error);
            alert("Error opening math editor. Please try again.");
        }
    }

    /**
     * Create dialog by loading the HTML template
     */
    createDialogDirectly() {
        console.log("Loading dialog from HTML template");

        // Load the HTML template
        const templateUrl = this.url + "/matheditor.html";

        // Create a temporary container
        const tempContainer = document.createElement("div");

        // Fetch the HTML template
        fetch(templateUrl)
            .then((response) => {
                if (!response.ok) {
                    throw new Error(
                        `Failed to load template: ${response.status} ${response.statusText}`
                    );
                }
                return response.text();
            })
            .then((html) => {
                // Set the HTML content
                tempContainer.innerHTML = html;

                // Get the dialog element
                this.dialog = tempContainer.querySelector(".matheditor-dialog");

                if (!this.dialog) {
                    throw new Error("Dialog element not found in template");
                }

                // Get the math field element
                this.mathField = this.dialog.querySelector("#matheditor-input");

                if (!this.mathField) {
                    throw new Error("Math field element not found in template");
                }

                // Add event listeners
                const closeBtn = this.dialog.querySelector(
                    ".matheditor-close-btn"
                );
                if (closeBtn) {
                    closeBtn.addEventListener("click", () =>
                        this.closeDialog()
                    );
                }

                const cancelBtn = this.dialog.querySelector(
                    ".matheditor-cancel-btn"
                );
                if (cancelBtn) {
                    cancelBtn.addEventListener("click", () =>
                        this.closeDialog()
                    );
                }

                const insertBtn = this.dialog.querySelector(
                    ".matheditor-insert-btn"
                );
                if (insertBtn) {
                    insertBtn.addEventListener("click", () =>
                        this.insertEquation()
                    );
                }

                // Create toolbar buttons
                this.createToolbarButtons();

                // Close when clicking outside
                this.dialog.addEventListener("click", (e) => {
                    if (e.target === this.dialog) {
                        this.closeDialog();
                    }
                });

                // Keyboard shortcuts
                document.addEventListener(
                    "keydown",
                    this.keydownHandler.bind(this)
                );

                // Add to document
                document.body.appendChild(this.dialog);
                console.log("Dialog added to document successfully");

                // Initialize MathLive
                this.ensureMathLive(() => {
                    this.initMathField();
                });
            })
            .catch((error) => {
                console.error("Error loading dialog template:", error);
                // Fallback to inline creation if template loading fails
                this.createDialogInline();
            });
    }

    /**
     * Create toolbar buttons with Google Docs style grouping
     */
    createToolbarButtons() {
        // Set up tab switching functionality
        this.setupTabSwitching();

        // Define symbol categories
        const symbolCategories = this.getSymbolCategories();

        // Create buttons for each category
        Object.keys(symbolCategories).forEach((category) => {
            const tabContent = this.dialog.querySelector(`#${category}-tab`);
            if (!tabContent) {
                console.error(`Tab content for ${category} not found`);
                return;
            }

            // Get symbols for this category
            const categorySymbols = symbolCategories[category];

            // Group symbols by subcategory
            const groupedSymbols =
                this.groupSymbolsBySubcategory(categorySymbols);

            // Create button groups
            this.createButtonGroups(tabContent, groupedSymbols);
        });
    }

    /**
     * Group symbols by subcategory
     */
    groupSymbolsBySubcategory(symbols) {
        const groups = {};

        symbols.forEach((symbol) => {
            const subcategory = symbol.subcategory || "General";
            if (!groups[subcategory]) {
                groups[subcategory] = [];
            }
            groups[subcategory].push(symbol);
        });

        return groups;
    }

    /**
     * Create compact horizontal ribbon groups
     */
    createButtonGroups(container, groupedSymbols) {
        const groupNames = Object.keys(groupedSymbols);

        groupNames.forEach((groupName, index) => {
            // Create button group container
            const buttonGroup = document.createElement("div");
            buttonGroup.className = "matheditor-button-group";
            buttonGroup.title = groupName; // Add tooltip for group identification

            // Add buttons directly to the group (horizontal layout)
            groupedSymbols[groupName].forEach((symbol) => {
                const btn = this.createSymbolButton(symbol);
                buttonGroup.appendChild(btn);
            });

            // Add to container
            container.appendChild(buttonGroup);

            // Add divider if not the last group
            if (index < groupNames.length - 1) {
                const divider = document.createElement("div");
                divider.className = "matheditor-group-divider";
                container.appendChild(divider);
            }
        });
    }

    /**
     * Set up tab switching functionality
     */
    setupTabSwitching() {
        const tabButtons = this.dialog.querySelectorAll(".matheditor-tab-btn");

        tabButtons.forEach((button) => {
            button.addEventListener("click", () => {
                // Remove active class from all tabs
                tabButtons.forEach((btn) => btn.classList.remove("active"));

                // Add active class to clicked tab
                button.classList.add("active");

                // Hide all tab content
                const tabContents = this.dialog.querySelectorAll(
                    ".matheditor-tab-content"
                );
                tabContents.forEach((content) =>
                    content.classList.remove("active")
                );

                // Show the selected tab content
                const tabName = button.getAttribute("data-tab");
                const selectedContent = this.dialog.querySelector(
                    `#${tabName}-tab`
                );
                if (selectedContent) {
                    selectedContent.classList.add("active");
                }
            });
        });
    }

    /**
     * Create a button for a symbol with FontAwesome icon
     */
    createSymbolButton(symbol) {
        const btn = document.createElement("button");
        btn.className = "matheditor-toolbar-btn";

        // Add large class for certain symbols
        if (symbol.large) {
            btn.classList.add("large");
        }

        // Create button content with icon if available
        if (symbol.icon) {
            // Create icon element
            const iconElement = document.createElement("i");

            // Add FontAwesome classes
            const iconClasses = symbol.icon.split(" ");
            iconClasses.forEach((cls) => {
                iconElement.classList.add(cls);
            });

            // Add icon to button
            btn.appendChild(iconElement);

            // If we have both icon and display text, add a span for the text
            if (symbol.display || symbol.text) {
                // For buttons with both icon and text, add a small space
                const textSpan = document.createElement("span");
                textSpan.className = "btn-text";
                textSpan.textContent = symbol.display || symbol.text;
                btn.appendChild(textSpan);
            }
        } else {
            // No icon, just use text
            btn.textContent = symbol.display || symbol.text;
        }

        btn.title = symbol.text || symbol.latex; // Use text as tooltip for better description

        btn.addEventListener("click", () => {
            try {
                // Insert symbol at cursor position using MathLive API
                if (this.mathField) {
                    this.mathField.focus();

                    // Insert the symbol
                    this.mathField.insert(symbol.latex);

                    // If the symbol has editable placeholders, select them
                    if (symbol.selectPlaceholder) {
                        // Wait a tiny bit for the insertion to complete
                        setTimeout(() => {
                            this.selectPlaceholder(symbol.selectPlaceholder);
                        }, 10);
                    }
                } else {
                    console.error("Math field not available");
                }
            } catch (error) {
                console.error("Error inserting symbol:", error);
            }
        });

        return btn;
    }

    /**
     * Select a placeholder in the math field
     * @param {string|object} placeholder - The placeholder to select
     */
    selectPlaceholder(placeholder) {
        try {
            if (!this.mathField) {
                return;
            }

            // If placeholder is a string, it's a simple selection
            if (typeof placeholder === "string") {
                // Find the placeholder in the LaTeX
                const latex = this.mathField.value;
                const placeholderIndex = latex.indexOf(placeholder);

                if (placeholderIndex >= 0) {
                    // Select the placeholder
                    this.mathField.setSelection(
                        placeholderIndex,
                        placeholderIndex + placeholder.length
                    );
                }
            }
            // If placeholder is an object, it has more complex selection logic
            else if (typeof placeholder === "object") {
                if (placeholder.type === "moveToSlot") {
                    // Move to a specific slot in the expression
                    // For example, move to the numerator in a fraction
                    this.mathField.executeCommand(
                        placeholder.command || "moveToNextChar"
                    );

                    // If there's a selection after moving, apply it
                    if (placeholder.select) {
                        this.mathField.executeCommand("selectAll");
                    }
                }
            }
        } catch (error) {
            console.error("Error selecting placeholder:", error);
        }
    }

    /**
     * Get symbol categories for the toolbar
     */
    getSymbolCategories() {
        return {
            // Basic symbols
            basic: [
                {
                    text: "Fraction",
                    latex: "\\frac{a}{b}",
                    icon: "fal fa-divide",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "Square Root",
                    display: "√",
                    latex: "\\sqrt{x}",
                    icon: "fal fa-square-root-alt",
                    selectPlaceholder: "x",
                },
                {
                    text: "Cube Root",
                    display: "∛",
                    latex: "\\sqrt[3]{x}",
                    icon: "fal fa-cube-root",
                    selectPlaceholder: "x",
                },
                {
                    text: "nth Root",
                    display: "ⁿ√",
                    latex: "\\sqrt[n]{x}",
                    icon: "fal fa-function",
                    selectPlaceholder: "n",
                },
                {
                    text: "Exponent",
                    display: "xⁿ",
                    latex: "x^{n}",
                    icon: "fal fa-superscript",
                    selectPlaceholder: "n",
                },
                {
                    text: "Subscript",
                    display: "xₙ",
                    latex: "x_{n}",
                    icon: "fal fa-subscript",
                    selectPlaceholder: "n",
                },
                {
                    text: "Superscript & Subscript",
                    display: "xₙᵐ",
                    latex: "x_{n}^{m}",
                    icon: "fal fa-sigma",
                    selectPlaceholder: "n",
                },
                {
                    text: "Infinity",
                    display: "∞",
                    latex: "\\infty",
                    icon: "fal fa-infinity",
                },
                {
                    text: "Parentheses",
                    display: "(x)",
                    latex: "\\left( x \\right)",
                    icon: "fal fa-brackets-round",
                    selectPlaceholder: "x",
                },
                {
                    text: "Brackets",
                    display: "[x]",
                    latex: "\\left[ x \\right]",
                    icon: "fal fa-brackets-square",
                    selectPlaceholder: "x",
                },
                {
                    text: "Braces",
                    display: "{x}",
                    latex: "\\left\\{ x \\right\\}",
                    icon: "fal fa-brackets-curly",
                    selectPlaceholder: "x",
                },
                {
                    text: "Absolute Value",
                    display: "|x|",
                    latex: "\\left| x \\right|",
                    icon: "fal fa-vertical-line",
                    selectPlaceholder: "x",
                },
            ],

            // Operators
            operators: [
                {
                    subcategory: "Basic Operators",
                    text: "Plus",
                    display: "+",
                    latex: "+",
                    icon: "fal fa-plus",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Minus",
                    display: "−",
                    latex: "-",
                    icon: "fal fa-minus",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Plus-Minus",
                    display: "±",
                    latex: "\\pm",
                    icon: "fal fa-plus-minus",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Minus-Plus",
                    display: "∓",
                    latex: "\\mp",
                    icon: "fal fa-minus-plus",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Multiplication",
                    display: "×",
                    latex: "\\times",
                    icon: "fal fa-times",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Division",
                    display: "÷",
                    latex: "\\div",
                    icon: "fal fa-divide",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Dot Product",
                    display: "⋅",
                    latex: "\\cdot",
                    icon: "fal fa-circle-dot",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Star",
                    display: "⋆",
                    latex: "\\ast",
                    icon: "fal fa-asterisk",
                },
                {
                    subcategory: "Basic Operators",
                    text: "Circle",
                    display: "◦",
                    latex: "\\circ",
                    icon: "fal fa-circle",
                },

                {
                    subcategory: "Set Operators",
                    text: "Union",
                    display: "∪",
                    latex: "\\cup",
                },
                {
                    subcategory: "Set Operators",
                    text: "Intersection",
                    display: "∩",
                    latex: "\\cap",
                },
                {
                    subcategory: "Set Operators",
                    text: "Set Minus",
                    display: "∖",
                    latex: "\\setminus",
                },
                {
                    subcategory: "Set Operators",
                    text: "Element Of",
                    display: "∈",
                    latex: "\\in",
                },
                {
                    subcategory: "Set Operators",
                    text: "Not Element Of",
                    display: "∉",
                    latex: "\\notin",
                },
                {
                    subcategory: "Set Operators",
                    text: "Contains",
                    display: "∋",
                    latex: "\\ni",
                },
                {
                    subcategory: "Set Operators",
                    text: "Subset",
                    display: "⊂",
                    latex: "\\subset",
                },
                {
                    subcategory: "Set Operators",
                    text: "Superset",
                    display: "⊃",
                    latex: "\\supset",
                },
                {
                    subcategory: "Set Operators",
                    text: "Subset or Equal",
                    display: "⊆",
                    latex: "\\subseteq",
                },
                {
                    subcategory: "Set Operators",
                    text: "Superset or Equal",
                    display: "⊇",
                    latex: "\\supseteq",
                },

                {
                    subcategory: "Calculus",
                    text: "Partial Derivative",
                    display: "∂",
                    latex: "\\partial",
                },
                {
                    subcategory: "Calculus",
                    text: "Integral",
                    display: "∫",
                    latex: "\\int",
                },
                {
                    subcategory: "Calculus",
                    text: "Double Integral",
                    display: "∬",
                    latex: "\\iint",
                },
                {
                    subcategory: "Calculus",
                    text: "Triple Integral",
                    display: "∭",
                    latex: "\\iiint",
                },
                {
                    subcategory: "Calculus",
                    text: "Contour Integral",
                    display: "∮",
                    latex: "\\oint",
                },
                {
                    subcategory: "Calculus",
                    text: "Surface Integral",
                    display: "∯",
                    latex: "\\oiint",
                },
                {
                    subcategory: "Calculus",
                    text: "Volume Integral",
                    display: "∰",
                    latex: "\\oiiint",
                },

                {
                    subcategory: "Big Operators",
                    text: "Sum",
                    display: "∑",
                    latex: "\\sum_{i=1}^{n}",
                    selectPlaceholder: "i=1",
                },
                {
                    subcategory: "Big Operators",
                    text: "Product",
                    display: "∏",
                    latex: "\\prod_{i=1}^{n}",
                    selectPlaceholder: "i=1",
                },
                {
                    subcategory: "Big Operators",
                    text: "Coproduct",
                    display: "∐",
                    latex: "\\coprod_{i=1}^{n}",
                    selectPlaceholder: "i=1",
                },
                {
                    subcategory: "Big Operators",
                    text: "Limit",
                    display: "lim",
                    latex: "\\lim_{x \\to 0}",
                    selectPlaceholder: "x \\to 0",
                },
            ],

            // Relations
            relations: [
                {
                    text: "Equals",
                    display: "=",
                    latex: "=",
                    icon: "fal fa-equals",
                },
                {
                    text: "Not Equal",
                    display: "≠",
                    latex: "\\ne",
                    icon: "fal fa-not-equal",
                },
                {
                    text: "Approximately",
                    display: "≈",
                    latex: "\\approx",
                    icon: "fal fa-wave-sine",
                },
                {
                    text: "Less Than",
                    display: "<",
                    latex: "<",
                    icon: "fal fa-less-than",
                },
                {
                    text: "Greater Than",
                    display: ">",
                    latex: ">",
                    icon: "fal fa-greater-than",
                },
                {
                    text: "Less Than or Equal",
                    display: "≤",
                    latex: "\\le",
                    icon: "fal fa-less-than-equal",
                },
                {
                    text: "Greater Than or Equal",
                    display: "≥",
                    latex: "\\ge",
                    icon: "fal fa-greater-than-equal",
                },
                {
                    text: "Much Less Than",
                    display: "≪",
                    latex: "\\ll",
                    icon: "fal fa-angle-double-left",
                },
                {
                    text: "Much Greater Than",
                    display: "≫",
                    latex: "\\gg",
                    icon: "fal fa-angle-double-right",
                },
                {
                    text: "Proportional To",
                    display: "∝",
                    latex: "\\propto",
                    icon: "fal fa-proportion",
                },
                {
                    text: "Parallel",
                    display: "∥",
                    latex: "\\parallel",
                    icon: "fal fa-grip-lines",
                },
                {
                    text: "Perpendicular",
                    display: "⊥",
                    latex: "\\perp",
                    icon: "fal fa-grip-lines-vertical",
                },
                {
                    text: "Similar",
                    display: "∼",
                    latex: "\\sim",
                    icon: "fal fa-wave-pulse",
                },
                {
                    text: "Congruent",
                    display: "≅",
                    latex: "\\cong",
                    icon: "fal fa-equals",
                },
                {
                    text: "Equivalent",
                    display: "≡",
                    latex: "\\equiv",
                    icon: "fal fa-horizontal-rule",
                },
            ],

            // Greek letters
            greek: [
                {
                    subcategory: "Lowercase",
                    text: "Alpha",
                    display: "α",
                    latex: "\\alpha",
                },
                {
                    subcategory: "Lowercase",
                    text: "Beta",
                    display: "β",
                    latex: "\\beta",
                },
                {
                    subcategory: "Lowercase",
                    text: "Gamma",
                    display: "γ",
                    latex: "\\gamma",
                },
                {
                    subcategory: "Lowercase",
                    text: "Delta",
                    display: "δ",
                    latex: "\\delta",
                },
                {
                    subcategory: "Lowercase",
                    text: "Epsilon",
                    display: "ε",
                    latex: "\\epsilon",
                },
                {
                    subcategory: "Lowercase",
                    text: "Zeta",
                    display: "ζ",
                    latex: "\\zeta",
                },
                {
                    subcategory: "Lowercase",
                    text: "Eta",
                    display: "η",
                    latex: "\\eta",
                },
                {
                    subcategory: "Lowercase",
                    text: "Theta",
                    display: "θ",
                    latex: "\\theta",
                },
                {
                    subcategory: "Lowercase",
                    text: "Iota",
                    display: "ι",
                    latex: "\\iota",
                },
                {
                    subcategory: "Lowercase",
                    text: "Kappa",
                    display: "κ",
                    latex: "\\kappa",
                },
                {
                    subcategory: "Lowercase",
                    text: "Lambda",
                    display: "λ",
                    latex: "\\lambda",
                },
                {
                    subcategory: "Lowercase",
                    text: "Mu",
                    display: "μ",
                    latex: "\\mu",
                },
                {
                    subcategory: "Lowercase",
                    text: "Nu",
                    display: "ν",
                    latex: "\\nu",
                },
                {
                    subcategory: "Lowercase",
                    text: "Xi",
                    display: "ξ",
                    latex: "\\xi",
                },
                {
                    subcategory: "Lowercase",
                    text: "Omicron",
                    display: "ο",
                    latex: "\\omicron",
                },
                {
                    subcategory: "Lowercase",
                    text: "Pi",
                    display: "π",
                    latex: "\\pi",
                },
                {
                    subcategory: "Lowercase",
                    text: "Rho",
                    display: "ρ",
                    latex: "\\rho",
                },
                {
                    subcategory: "Lowercase",
                    text: "Sigma",
                    display: "σ",
                    latex: "\\sigma",
                },
                {
                    subcategory: "Lowercase",
                    text: "Tau",
                    display: "τ",
                    latex: "\\tau",
                },
                {
                    subcategory: "Lowercase",
                    text: "Upsilon",
                    display: "υ",
                    latex: "\\upsilon",
                },
                {
                    subcategory: "Lowercase",
                    text: "Phi",
                    display: "φ",
                    latex: "\\phi",
                },
                {
                    subcategory: "Lowercase",
                    text: "Chi",
                    display: "χ",
                    latex: "\\chi",
                },
                {
                    subcategory: "Lowercase",
                    text: "Psi",
                    display: "ψ",
                    latex: "\\psi",
                },
                {
                    subcategory: "Lowercase",
                    text: "Omega",
                    display: "ω",
                    latex: "\\omega",
                },

                {
                    subcategory: "Uppercase",
                    text: "Alpha",
                    display: "Α",
                    latex: "A",
                },
                {
                    subcategory: "Uppercase",
                    text: "Beta",
                    display: "Β",
                    latex: "B",
                },
                {
                    subcategory: "Uppercase",
                    text: "Gamma",
                    display: "Γ",
                    latex: "\\Gamma",
                },
                {
                    subcategory: "Uppercase",
                    text: "Delta",
                    display: "Δ",
                    latex: "\\Delta",
                },
                {
                    subcategory: "Uppercase",
                    text: "Epsilon",
                    display: "Ε",
                    latex: "E",
                },
                {
                    subcategory: "Uppercase",
                    text: "Zeta",
                    display: "Ζ",
                    latex: "Z",
                },
                {
                    subcategory: "Uppercase",
                    text: "Eta",
                    display: "Η",
                    latex: "H",
                },
                {
                    subcategory: "Uppercase",
                    text: "Theta",
                    display: "Θ",
                    latex: "\\Theta",
                },
                {
                    subcategory: "Uppercase",
                    text: "Iota",
                    display: "Ι",
                    latex: "I",
                },
                {
                    subcategory: "Uppercase",
                    text: "Kappa",
                    display: "Κ",
                    latex: "K",
                },
                {
                    subcategory: "Uppercase",
                    text: "Lambda",
                    display: "Λ",
                    latex: "\\Lambda",
                },
                {
                    subcategory: "Uppercase",
                    text: "Mu",
                    display: "Μ",
                    latex: "M",
                },
                {
                    subcategory: "Uppercase",
                    text: "Nu",
                    display: "Ν",
                    latex: "N",
                },
                {
                    subcategory: "Uppercase",
                    text: "Xi",
                    display: "Ξ",
                    latex: "\\Xi",
                },
                {
                    subcategory: "Uppercase",
                    text: "Omicron",
                    display: "Ο",
                    latex: "O",
                },
                {
                    subcategory: "Uppercase",
                    text: "Pi",
                    display: "Π",
                    latex: "\\Pi",
                },
                {
                    subcategory: "Uppercase",
                    text: "Rho",
                    display: "Ρ",
                    latex: "P",
                },
                {
                    subcategory: "Uppercase",
                    text: "Sigma",
                    display: "Σ",
                    latex: "\\Sigma",
                },
                {
                    subcategory: "Uppercase",
                    text: "Tau",
                    display: "Τ",
                    latex: "T",
                },
                {
                    subcategory: "Uppercase",
                    text: "Upsilon",
                    display: "Υ",
                    latex: "\\Upsilon",
                },
                {
                    subcategory: "Uppercase",
                    text: "Phi",
                    display: "Φ",
                    latex: "\\Phi",
                },
                {
                    subcategory: "Uppercase",
                    text: "Chi",
                    display: "Χ",
                    latex: "X",
                },
                {
                    subcategory: "Uppercase",
                    text: "Psi",
                    display: "Ψ",
                    latex: "\\Psi",
                },
                {
                    subcategory: "Uppercase",
                    text: "Omega",
                    display: "Ω",
                    latex: "\\Omega",
                },
            ],

            // Arrows
            arrows: [
                {
                    text: "Left Arrow",
                    display: "←",
                    latex: "\\leftarrow",
                    icon: "fal fa-arrow-left",
                },
                {
                    text: "Right Arrow",
                    display: "→",
                    latex: "\\rightarrow",
                    icon: "fal fa-arrow-right",
                },
                {
                    text: "Up Arrow",
                    display: "↑",
                    latex: "\\uparrow",
                    icon: "fal fa-arrow-up",
                },
                {
                    text: "Down Arrow",
                    display: "↓",
                    latex: "\\downarrow",
                    icon: "fal fa-arrow-down",
                },
                {
                    text: "Left-Right Arrow",
                    display: "↔",
                    latex: "\\leftrightarrow",
                    icon: "fal fa-arrows-left-right",
                },
                {
                    text: "Up-Down Arrow",
                    display: "↕",
                    latex: "\\updownarrow",
                    icon: "fal fa-arrows-up-down",
                },
                {
                    text: "Double Left Arrow",
                    display: "⇐",
                    latex: "\\Leftarrow",
                    icon: "fal fa-arrow-left-long",
                },
                {
                    text: "Double Right Arrow",
                    display: "⇒",
                    latex: "\\Rightarrow",
                    icon: "fal fa-arrow-right-long",
                },
                {
                    text: "Double Up Arrow",
                    display: "⇑",
                    latex: "\\Uparrow",
                    icon: "fal fa-arrow-up-long",
                },
                {
                    text: "Double Down Arrow",
                    display: "⇓",
                    latex: "\\Downarrow",
                    icon: "fal fa-arrow-down-long",
                },
                {
                    text: "Double Left-Right Arrow",
                    display: "⇔",
                    latex: "\\Leftrightarrow",
                    icon: "fal fa-arrows-left-right-to-line",
                },
                {
                    text: "Double Up-Down Arrow",
                    display: "⇕",
                    latex: "\\Updownarrow",
                    icon: "fal fa-arrows-up-down-to-line",
                },
                {
                    text: "Maps To",
                    display: "↦",
                    latex: "\\mapsto",
                    icon: "fal fa-right-to-line",
                },
                {
                    text: "Long Right Arrow",
                    display: "⟶",
                    latex: "\\longrightarrow",
                    icon: "fal fa-arrow-right-to-line",
                },
                {
                    text: "Long Left Arrow",
                    display: "⟵",
                    latex: "\\longleftarrow",
                    icon: "fal fa-arrow-left-to-line",
                },
                {
                    text: "Long Left-Right Arrow",
                    display: "⟷",
                    latex: "\\longleftrightarrow",
                    icon: "fal fa-left-right-to-line",
                },
            ],

            // Functions
            functions: [
                {
                    text: "Sine",
                    display: "sin",
                    latex: "\\sin",
                    icon: "fal fa-function",
                },
                {
                    text: "Cosine",
                    display: "cos",
                    latex: "\\cos",
                    icon: "fal fa-function",
                },
                {
                    text: "Tangent",
                    display: "tan",
                    latex: "\\tan",
                    icon: "fal fa-function",
                },
                {
                    text: "Cotangent",
                    display: "cot",
                    latex: "\\cot",
                    icon: "fal fa-function",
                },
                {
                    text: "Secant",
                    display: "sec",
                    latex: "\\sec",
                    icon: "fal fa-function",
                },
                {
                    text: "Cosecant",
                    display: "csc",
                    latex: "\\csc",
                    icon: "fal fa-function",
                },
                {
                    text: "Arcsin",
                    display: "arcsin",
                    latex: "\\arcsin",
                    icon: "fal fa-inverse",
                },
                {
                    text: "Arccos",
                    display: "arccos",
                    latex: "\\arccos",
                    icon: "fal fa-inverse",
                },
                {
                    text: "Arctan",
                    display: "arctan",
                    latex: "\\arctan",
                    icon: "fal fa-inverse",
                },
                {
                    text: "Sinh",
                    display: "sinh",
                    latex: "\\sinh",
                    icon: "fal fa-wave-sine",
                },
                {
                    text: "Cosh",
                    display: "cosh",
                    latex: "\\cosh",
                    icon: "fal fa-wave-sine",
                },
                {
                    text: "Tanh",
                    display: "tanh",
                    latex: "\\tanh",
                    icon: "fal fa-wave-sine",
                },
                {
                    text: "Log",
                    display: "log",
                    latex: "\\log",
                    icon: "fal fa-chart-line",
                },
                {
                    text: "Ln",
                    display: "ln",
                    latex: "\\ln",
                    icon: "fal fa-chart-line",
                },
                {
                    text: "Determinant",
                    display: "det",
                    latex: "\\det",
                    icon: "fal fa-table",
                },
                {
                    text: "Dimension",
                    display: "dim",
                    latex: "\\dim",
                    icon: "fal fa-ruler-combined",
                },
                {
                    text: "Minimum",
                    display: "min",
                    latex: "\\min",
                    icon: "fal fa-arrow-down-to-line",
                },
                {
                    text: "Maximum",
                    display: "max",
                    latex: "\\max",
                    icon: "fal fa-arrow-up-to-line",
                },
                {
                    text: "Modulo",
                    display: "mod",
                    latex: "\\bmod",
                    icon: "fal fa-percent",
                },
                {
                    text: "Supremum",
                    display: "sup",
                    latex: "\\sup",
                    icon: "fal fa-arrow-up-to-line",
                },
                {
                    text: "Infimum",
                    display: "inf",
                    latex: "\\inf",
                    icon: "fal fa-arrow-down-to-line",
                },
                {
                    text: "Limit",
                    display: "lim",
                    latex: "\\lim",
                    icon: "fal fa-right-to-line",
                },
                {
                    text: "Exponential",
                    display: "exp",
                    latex: "\\exp",
                    icon: "fal fa-superscript",
                },
                {
                    text: "Natural Log",
                    display: "ln",
                    latex: "\\ln",
                    icon: "fal fa-chart-line",
                },
            ],

            // Matrices
            matrices: [
                {
                    text: "2×2 Matrix",
                    display: "2×2",
                    latex: "\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}",
                    icon: "fal fa-table-cells",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "3×3 Matrix",
                    display: "3×3",
                    latex: "\\begin{pmatrix} a & b & c \\\\ d & e & f \\\\ g & h & i \\end{pmatrix}",
                    icon: "fal fa-table-cells-large",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "2×2 Determinant",
                    display: "|2×2|",
                    latex: "\\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix}",
                    icon: "fal fa-table-cells",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "3×3 Determinant",
                    display: "|3×3|",
                    latex: "\\begin{vmatrix} a & b & c \\\\ d & e & f \\\\ g & h & i \\end{vmatrix}",
                    icon: "fal fa-table-cells-large",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "2×2 Brackets Matrix",
                    display: "[2×2]",
                    latex: "\\begin{bmatrix} a & b \\\\ c & d \\end{bmatrix}",
                    icon: "fal fa-brackets-square",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "3×3 Brackets Matrix",
                    display: "[3×3]",
                    latex: "\\begin{bmatrix} a & b & c \\\\ d & e & f \\\\ g & h & i \\end{bmatrix}",
                    icon: "fal fa-brackets-square",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "2×2 Braces Matrix",
                    display: "{2×2}",
                    latex: "\\begin{Bmatrix} a & b \\\\ c & d \\end{Bmatrix}",
                    icon: "fal fa-brackets-curly",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "3×3 Braces Matrix",
                    display: "{3×3}",
                    latex: "\\begin{Bmatrix} a & b & c \\\\ d & e & f \\\\ g & h & i \\end{Bmatrix}",
                    icon: "fal fa-brackets-curly",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
                {
                    text: "Cases",
                    display: "cases",
                    latex: "\\begin{cases} y=x & \\text{if } x \\geq 0 \\\\ y=-x & \\text{if } x < 0 \\end{cases}",
                    icon: "fal fa-code-branch",
                    large: true,
                    selectPlaceholder: {
                        type: "moveToSlot",
                        command: "moveToMathFieldStart",
                        select: true,
                    },
                },
            ],
        };
    }

    /**
     * Fallback method to create dialog inline if template loading fails
     */
    createDialogInline() {
        console.warn("Falling back to inline dialog creation");

        // Create main dialog container
        this.dialog = document.createElement("div");
        this.dialog.className = "matheditor-dialog";

        // Create content container
        const content = document.createElement("div");
        content.className = "matheditor-content";

        // Create header
        const header = document.createElement("div");
        header.className = "matheditor-header";

        const title = document.createElement("h2");
        title.textContent = "Math Editor";

        const closeBtn = document.createElement("button");
        closeBtn.className = "matheditor-close-btn";
        closeBtn.innerHTML = "&times;";
        closeBtn.title = "Close";
        closeBtn.addEventListener("click", () => this.closeDialog());

        header.appendChild(title);
        header.appendChild(closeBtn);

        // Create input container
        const inputContainer = document.createElement("div");
        inputContainer.className = "matheditor-input-container";

        const inputLabel = document.createElement("label");
        inputLabel.className = "matheditor-label";
        inputLabel.textContent = "Edit Equation:";

        this.mathField = document.createElement("math-field");
        this.mathField.id = "matheditor-input";

        inputContainer.appendChild(inputLabel);
        inputContainer.appendChild(this.mathField);

        // Create toolbar
        const toolbar = document.createElement("div");
        toolbar.className = "matheditor-toolbar";

        // Create buttons container
        const buttonsContainer = document.createElement("div");
        buttonsContainer.className = "matheditor-buttons-container";

        const cancelBtn = document.createElement("button");
        cancelBtn.className = "matheditor-cancel-btn";
        cancelBtn.textContent = "Cancel";
        cancelBtn.addEventListener("click", () => this.closeDialog());

        const insertBtn = document.createElement("button");
        insertBtn.className = "matheditor-insert-btn";
        insertBtn.textContent = "Insert";
        insertBtn.addEventListener("click", () => this.insertEquation());

        buttonsContainer.appendChild(cancelBtn);
        buttonsContainer.appendChild(insertBtn);

        // Assemble dialog
        content.appendChild(header);
        content.appendChild(inputContainer);
        content.appendChild(toolbar);
        content.appendChild(buttonsContainer);
        this.dialog.appendChild(content);

        // Close when clicking outside
        this.dialog.addEventListener("click", (e) => {
            if (e.target === this.dialog) {
                this.closeDialog();
            }
        });

        // Keyboard shortcuts
        document.addEventListener("keydown", this.keydownHandler.bind(this));

        // Add to document
        document.body.appendChild(this.dialog);

        // Create toolbar buttons
        this.createToolbarButtons();
    }

    /**
     * Initialize the math field
     */
    initMathField() {
        try {
            // Initialize the math field with MathLive
            this.mathField.mathVirtualKeyboardPolicy = "off"; // Disable virtual keyboard
            this.mathField.virtualKeyboardToggleGlyph = null; // Remove the virtual keyboard toggle button
            this.mathField.smartMode = true;
            this.mathField.smartFence = true;
            this.mathField.letterShapeStyle = "tex";
            this.mathField.style.fontSize = "18px";

            console.log("MathLive field initialized successfully");

            // Set initial value if provided
            if (this.initialLatex) {
                this.mathField.value = this.initialLatex;
                console.log("Initial LaTeX set:", this.initialLatex);
            }

            // Set up click handler for editable elements
            this.setupEditableElementsClickHandler();

            // Focus the math field
            setTimeout(() => {
                this.mathField.focus();
            }, 200);

            // Also try to remove keyboard toggle programmatically
            setTimeout(() => {
                const toggleButton = document.querySelector(
                    '.ML__virtual-keyboard-toggle, [part="virtual-keyboard-toggle"]'
                );
                if (toggleButton) {
                    toggleButton.remove();
                    console.log(
                        "Virtual keyboard toggle button removed programmatically"
                    );
                }
            }, 500);
        } catch (e) {
            console.error("Error initializing MathLive field:", e);
            alert("Error initializing the math editor. Please try again.");
            this.closeDialog();
        }
    }

    /**
     * Set up click handler for editable elements within the math field
     */
    setupEditableElementsClickHandler() {
        if (!this.mathField) {
            return;
        }

        console.log("Setting up editable elements click handler");

        // Add click event listener to the math field
        this.mathField.addEventListener("click", () => {
            // When a user clicks on an element in the math field,
            // we want to make that element editable immediately

            try {
                // Get the current position in the math field
                const position = this.mathField.selectionStart;

                if (position !== undefined) {
                    // Try to select the most appropriate element at the current position

                    // First, try to select the current token/atom
                    // This uses MathLive's command to select the current group (like a fraction, exponent, etc.)
                    this.mathField.executeCommand("selectGroup");

                    // Check if we selected something
                    if (
                        this.mathField.selectionStart ===
                        this.mathField.selectionEnd
                    ) {
                        // If nothing was selected, try to select the parent expression
                        this.mathField.executeCommand("selectParent");

                        // If still nothing selected, try to select the current word
                        if (
                            this.mathField.selectionStart ===
                            this.mathField.selectionEnd
                        ) {
                            this.mathField.executeCommand("selectWord");
                        }
                    }

                    // If we have a selection now, log it
                    if (
                        this.mathField.selectionStart !==
                        this.mathField.selectionEnd
                    ) {
                        console.log("Element clicked and selected for editing");

                        // Get the selected LaTeX
                        const selectedLatex = this.mathField.getValue(
                            "latex-expanded",
                            {
                                selection: true,
                                selectionMode: "item",
                            }
                        );

                        console.log("Selected LaTeX:", selectedLatex);
                    } else {
                        console.log(
                            "No element could be selected at click position"
                        );
                    }
                }
            } catch (error) {
                console.error("Error handling editable element click:", error);
            }
        });

        // Also add a handler for double-click to select the entire expression
        this.mathField.addEventListener("dblclick", () => {
            try {
                // Select the entire expression at the current position
                this.mathField.executeCommand("selectAll");
                console.log("Double-clicked: entire expression selected");
            } catch (error) {
                console.error("Error handling double-click:", error);
            }
        });

        console.log("Editable elements click handler set up successfully");
    }

    /**
     * Insert equation into editor
     */
    insertEquation() {
        const latex = this.mathField.value;
        if (latex && latex.trim() !== "") {
            try {
                // Create the URL with proper encoding
                const imageUrl = this.createLatexUrl(latex);

                // Log for debugging
                console.log("Inserting equation with LaTeX:", latex);
                console.log("Image URL:", imageUrl);

                // Create the HTML for the equation
                const escapedLatex = latex.replace(/"/g, "&quot;");
                const html =
                    '<span class="math-tex" data-latex="' +
                    escapedLatex +
                    '">' +
                    '<img src="' +
                    imageUrl +
                    '" alt="LaTeX Equation" ' +
                    'style="vertical-align: middle; max-width: 100%; height: auto;" title="Double-click to edit">' +
                    "</span>";

                // Log the HTML for debugging
                console.log("Generated HTML:", html);

                // Check if we're editing an existing equation
                const selection = this.editor.selection;
                const selectedNode = selection.getNode();
                const isEditing =
                    selectedNode.nodeName === "IMG" &&
                    selectedNode.parentNode &&
                    selectedNode.parentNode.className === "math-tex";

                if (isEditing) {
                    // Replace the existing equation
                    const mathSpan = selectedNode.parentNode;
                    mathSpan.outerHTML = html;
                    console.log("Replaced existing equation");
                } else {
                    // Insert new equation
                    this.editor.execCommand("mceInsertContent", false, html);
                    console.log("Inserted new equation");
                }

                // Close the dialog
                this.closeDialog();
            } catch (e) {
                console.error("Error inserting equation:", e);
                alert("Error inserting equation: " + e.message);
            }
        } else {
            alert("Please enter a LaTeX equation before inserting.");
        }
    }

    /**
     * Close the dialog
     */
    closeDialog() {
        if (this.dialog && document.body.contains(this.dialog)) {
            document.body.removeChild(this.dialog);
            document.removeEventListener("keydown", this.keydownHandler);
        }
    }

    /**
     * Handle keyboard events
     */
    keydownHandler(e) {
        if (e.key === "Escape") {
            if (this.dialog && document.body.contains(this.dialog)) {
                this.closeDialog();
                e.preventDefault();
            }
        }

        if ((e.ctrlKey || e.metaKey) && e.key === "Enter") {
            if (this.dialog && document.body.contains(this.dialog)) {
                this.insertEquation();
                e.preventDefault();
            }
        }
    }
}

// Export the MathEditor class
window.MathEditor = MathEditor;
